#!/bin/bash

# chmod +x new_path.sh
# sudo ./new_path.sh 
# sudo chown -R $USER:$USER /mnt/datasets/nnUnet/nnUnet_raw
# sudo chown -R $USER:$USER /mnt/datasets/nnUnet/nnUnet_preprocessed
# sudo chown -R $USER:$USER /mnt/results/nnUnet_results

# Crée la structure nécessaire pour nnU-Net avec les permissions root

RAW_PATH="/mnt/datasets/nnUnet/nnUnet_raw"
PREPROCESSED_PATH="/mnt/datasets/nnUnet/nnUnet_preprocessed"
RESULTS_PATH="/mnt/results/nnUnet_results"

# S'assurer que le script est exécuté avec les droits root
if [ "$EUID" -ne 0 ]; then
  echo "❌ Ce script doit être exécuté avec les droits root (sudo)." >&2
  exit 1
fi

# Création des dossiers
echo "🔧 Création des dossiers..."
mkdir -p "$RAW_PATH"
mkdir -p "$PREPROCESSED_PATH"
mkdir -p "$RESULTS_PATH"

# Attribution des permissions (lecture/écriture pour tous si nécessaire)
chmod -R 775 /mnt/datasets/nnUnet
chmod -R 775 /mnt/results/nnUnet_results

# Optionnel : changer le propriétaire (ex: pour un user spécifique)
# chown -R gabriel:gabriel /mnt/datasets/nnUnet
# chown -R gabriel:gabriel /mnt/results/nnUnet_results

echo "✅ Structure créée avec succès :"
echo "  - $RAW_PATH"
echo "  - $PREPROCESSED_PATH"
echo "  - $RESULTS_PATH"
